{"name": "infinity-notes", "version": "1.0.0", "description": "InfinityNotes - 无限便签，一个结合了无限画布和 AI 能力的便签应用", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "lint": "eslint server/**/*.js public/js/**/*.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "helmet": "^7.1.0", "leader-line": "^1.0.8", "openai": "^4.90.0", "sqlite3": "5.1.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^11.1.0", "ws": "^8.18.1"}, "devDependencies": {"eslint": "^8.55.0", "nodemon": "^2.0.22"}, "type": "module", "engines": {"node": ">=16.0.0"}}