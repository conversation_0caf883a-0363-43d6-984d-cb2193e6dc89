# Code Citations

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="current
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="current
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="current
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="current
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="current
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round"
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round"
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round"
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round"
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round"
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round"
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z">
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z">
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z">
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z">
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z">
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z">
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z">
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z">
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>

```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
    <
```

## License: 未知

https://github.com/saki1001/calamara/blob/71c6b996d015cfcd31b1c16d462f7c51327fed0e/wp-content/plugins/ml-slider/admin/views/pages/upgrade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
    <
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
    <
```

## License: MIT

https://github.com/podlove/podlove-publisher/blob/c7200b446e778d5dd7cbcab34f7032a717760dc5/js/src/components/icons/Eye.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
    <
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
    <
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
    <
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
    <
```

## License: CC-BY-SA-4.0

https://github.com/LCC-CIT/CS296N-CourseMaterials/blob/ed9e9f3c591f355a0b45b4de985fab103b4a24af/ArticlesAndNotes/How%20To%20Use%20Apache%20JMeter%20To%20Perform%20Load%20Testing%20on%20a%20Web%20Server%20%7C%20DigitalOcean.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
        <circle cx="12" cy="12" r="3"></circle>
    <
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon"
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon"
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20"
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20"
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20"
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20"
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20"
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg
```

## License: MIT

https://github.com/microweber/microweber/blob/032b7f030209520bb05d848cfdad1a49859e156e/src/MicroweberPackages/Content/resources/views/admin/content/livewire/components/display-as.blade.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: 未知

https://github.com/phpmyadmin/themes/blob/d882f603a0ff107bfb7111efb7d4e60e452ec67b/fistu/scss/_fistu-icons.scss

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: 未知

https://github.com/kushdilip/learning/blob/dde8ca883c38a7260f7809a321b402e277eb0d67/.obsidian/plugins/obsidian-git/main.js

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: MIT

https://github.com/ecomba/blog/blob/032804cbf998c014060523184178b69d47a9e6a8/_pages/tags.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: 未知

https://github.com/OvRcode/ovrride/blob/4a44a96a885d0fc3ac1d9edab5d25fc9e02896ce/wp-content/plugins/ml-slider-pro/modules/schedule/schedule.php

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: 未知

https://github.com/imweb/imweb.github.io/blob/cc90bb3480f3d14ba4e213985afa98e2a2583eba/imui/components-input-input/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: 未知

https://github.com/tthallos/vue-feather-icon/blob/598b01c91e141b885894223bc7fcb9ceb44fb011/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: MIT

https://github.com/itsamoreh/vue-base/blob/3bb53774adbd7a942603b68a932ba7be859af297/src/icons/components/eye-off.vue

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: 未知

https://github.com/marcorichetta/enredarte/blob/41a953ba208ef9a6d1c440c2ccd28e22783b564e/templates/sidebar.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```

## License: 未知

https://github.com/iamarpitpatidar/Auth-form/blob/f7d01ca1b3581b91fd6a2b447a885709d0a6755b/index.html

```
icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round">
        <path
            d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24">
        </path>
        <line x1="1" y1="1" x2="23" y2="23"></line>
    </svg>
</button
```
