<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>便签画布分享</title>
  <!-- 引入基础样式 -->
  <link rel="stylesheet" href="/css/base.css">
  <!-- 字体优化样式 - 改善文本在不同分辨率下的显示效果 -->
  <link rel="stylesheet" href="/css/font-optimization.css">
  <!-- 引入画布样式 -->
  <link rel="stylesheet" href="/css/canvas.css">
  <!-- 引入便签样式 -->
  <link rel="stylesheet" href="/css/notes.css">
  <!-- 引入Markdown样式 -->
  <link rel="stylesheet" href="/css/markdown.css">
  <!-- 引入分享页面特定样式 -->
  <link rel="stylesheet" href="/css/share.css">
  <link rel="icon" href="/favicon.ico" type="image/x-icon">
  <!-- 添加 marked.js 库用于Markdown渲染 -->
  <script src="https://cdn.jsdelivr.net/npm/marked@3.0.8/marked.min.js" defer></script>
  <!-- 添加 highlight.js 用于代码高亮，使用defer延迟加载 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.3.1/styles/github.min.css">
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.3.1/highlight.min.js" defer></script>
</head>

<body>
  <div class="share-container">
    <div class="share-header">
      <h1 class="share-title" id="canvas-title">InfinityNotes 分享</h1>
      <div class="share-info">
        <span class="share-id">分享ID: <span id="share-id-display"></span></span>
        <span class="share-time">最后更新: <span id="last-updated"></span></span>
        <button id="refresh-button" class="refresh-button">刷新数据</button>
      </div>
    </div>

    <!-- 画布容器区域 - 与主页面保持一致的结构 -->
    <div class="canvas-container">
      <div id="note-canvas">
        <!-- 背景元素和便签容器将通过JS动态添加，与主页面保持一致 -->
      </div>
    </div>

    <!-- 新的悬浮提示 -->
    <div class="floating-tip" id="share-floating-tip">
      <div class="tip-content">
        <span class="tip-icon">💡</span>
        <span>拖动画布可以移动视图，按住Ctrl键滚动鼠标可以缩放画布</span>
        <button class="tip-close" id="close-tip">&times;</button>
      </div>
    </div>
  </div>

  <script type="module" src="/js/share.js"></script>
</body>

</html>