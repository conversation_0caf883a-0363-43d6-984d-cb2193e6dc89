/**
 * 登录功能模块
 * 处理用户登录流程和状态检查
 */

/**
 * 初始化登录功能
 */
export function initLogin() {
  const loginForm = document.querySelector(".login-form");
  const usernameInput = document.getElementById("username");
  const passwordInput = document.getElementById("password");
  const loginButton = document.getElementById("login-button");
  const messageContainer = document.getElementById("login-message");

  // 检查是否已登录
  checkLoginStatus();

  // 检查是否是从账户删除后重定向过来的
  checkAccountDeletedStatus();

  // 登录按钮点击事件
  loginButton.addEventListener("click", handleLogin);

  // 输入框回车事件
  passwordInput.addEventListener("keydown", (e) => {
    if (e.key === "Enter") {
      handleLogin();
    }
  });

  /**
   * 检查账户删除状态
   */
  function checkAccountDeletedStatus() {
    const accountDeleted = sessionStorage.getItem("accountDeleted");
    if (accountDeleted === "true") {
      // 显示账户已删除的消息
      showMessage("您的账户已被管理员删除，请联系管理员获取更多信息", "error");
      // 清除状态，避免刷新页面后仍然显示
      sessionStorage.removeItem("accountDeleted");
    }
  }

  /**
   * 处理登录请求
   */
  async function handleLogin() {
    // 清除之前的消息
    clearMessage();

    // 获取输入值
    const username = usernameInput.value.trim();
    const password = passwordInput.value.trim();

    // 简单验证
    if (!username || !password) {
      showMessage("请输入用户名和密码", "error");
      return;
    }

    try {
      // 禁用登录按钮，防止重复提交
      loginButton.disabled = true;
      loginButton.textContent = "登录中...";

      // 发送登录请求
      const response = await fetch("/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (data.success) {
        showMessage("登录成功，正在跳转...", "success");
        // 登录成功后跳转到主页
        setTimeout(() => {
          window.location.href = "/";
        }, 1000);
      } else {
        showMessage(data.message || "登录失败", "error");
        loginButton.disabled = false;
        loginButton.textContent = "登录";
      }
    } catch (error) {
      console.error("登录请求失败:", error);
      showMessage("网络错误，请稍后重试", "error");
      loginButton.disabled = false;
      loginButton.textContent = "登录";
    }
  }

  /**
   * 检查登录状态
   */
  async function checkLoginStatus() {
    try {
      const response = await fetch("/api/session");
      const data = await response.json();

      if (data.success && data.isLoggedIn) {
        // 已登录，跳转到主页
        window.location.href = "/";
      }
    } catch (error) {
      console.error("检查登录状态失败:", error);
    }
  }

  /**
   * 显示消息
   */
  function showMessage(message, type) {
    messageContainer.textContent = message;
    messageContainer.className = "message-container " + type;
  }

  /**
   * 清除消息
   */
  function clearMessage() {
    messageContainer.textContent = "";
    messageContainer.className = "message-container";
  }
}

export default { initLogin };
