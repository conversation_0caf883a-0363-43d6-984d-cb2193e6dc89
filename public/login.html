<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InfinityNotes - 登录</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/login.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <meta http-equiv="Content-Security-Policy"
        content="default-src 'self'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com;">
</head>

<body>
    <div class="login-container">
        <div class="login-banner">
            <div class="banner-grid"></div>
            <div class="banner-decor banner-decor-1"></div>
            <div class="banner-decor banner-decor-2"></div>
            <div class="banner-decor banner-decor-3"></div>

            <div class="brand">
                <img src="img/logo.png" alt="Logo" class="banner-logo">
                <span>InfinityNotes - 无限便签</span>
            </div>
            <div class="banner-content">
                <h1>智能创作<br>无限可能</h1>
                <p>让AI助力你的创意，轻松将想法转化为精美便签</p>
            </div>
        </div>
        <div class="login-form-container">
            <div class="login-form-wrapper">
                <div class="login-header">
                    <h2>欢迎回来</h2>
                    <p>请登录您的账户</p>
                </div>

                <div class="login-form">
                    <div class="message-container" id="login-message"></div>

                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" placeholder="请输入用户名" autocomplete="username">
                    </div>

                    <div id="password-container">
                        <!-- 密码输入组件将在这里动态创建 -->
                    </div>

                    <button type="button" id="login-button" class="login-button">登录</button>

                    <div class="login-options">
                        <div class="register-prompt">
                            <p>没有账户？ <a href="/register.html">使用邀请码注册</a></p>
                        </div>
                    </div>

                    <div class="login-footer">
                        <p>InfinityNotes - 无限便签 · 版本 1.0.0</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/login.js" type="module"></script>
</body>

</html>