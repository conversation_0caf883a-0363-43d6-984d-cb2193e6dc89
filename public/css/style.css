* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  background-color: #fcfcfc;
}

/* 按钮基本样式 */
button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

/* 以下底部控制栏相关样式已移至 bottom-bar.css，此处注释掉 */
/*
.bottom-bar {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 20px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  padding: 16px;
  max-width: 850px;
  width: 80%;
  border: 1px solid #e8e8e8;
  overflow: hidden;
  z-index: 9999;
}

.bottom-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.bottom-left-actions {
  display: flex;
  align-items: center;
  gap: 5px;
}

.ai-model {
  background-color: #eef3ff;
  color: #666666;
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.web-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f4ff;
  color: #1a73e8;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 16px;
  padding: 0;
}

.web-button:hover {
  background-color: #e8f0fe;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(26, 115, 232, 0.2);
}

.web-button:active {
  transform: translateY(0);
}
*/

/* 以下抽屉相关样式已移至 drawer.css，此处注释掉 */
/*
.web-drawer {
  position: fixed;
  bottom: -95vh;
  left: 10px;
  right: 10px;
  width: calc(100% - 20px);
  max-width: none;
  transform: none;
  height: 95vh;
  background-color: white;
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  transition: bottom 0.3s ease;
  display: flex;
  flex-direction: column;
  border-radius: 16px 16px 0 0;
}

.web-drawer.open {
  bottom: 0;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #e8e8e8;
  height: 40px;
}

.drawer-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.drawer-close {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #f5f5f5;
  color: #666;
  border: none;
  font-size: 14px;
  transition: background-color 0.2s;
  padding: 0;
  line-height: 1;
}

.drawer-close:hover {
  background-color: #e0e0e0;
}

.drawer-drag-indicator {
  width: 40px;
  height: 4px;
  background-color: #ddd;
  border-radius: 2px;
  margin: 4px auto;
}

.drawer-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
}

.drawer-overlay.visible {
  opacity: 1;
  visibility: visible;
}
*/

/* 以下标签页相关样式已移至 tabs.css，此处注释掉 */
/*
.tabs-container {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  background-color: #f5f5f5;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabs-container::-webkit-scrollbar {
  display: none;
}

.tab {
  padding: 12px 16px;
  font-size: 14px;
  border: none;
  background: transparent;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  white-space: nowrap;
  color: #666;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab.active {
  border-bottom-color: #1a73e8;
  color: #1a73e8;
  background-color: #fff;
}

.tab:hover:not(.active) {
  background-color: #eee;
}

.tab-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-close {
  margin-left: 6px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  opacity: 0.5;
  transition: opacity 0.2s;
  color: #666;
  background-color: transparent;
}

.tab:hover .tab-close {
  opacity: 1;
}

.tab-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.add-tab {
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  margin: 6px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.add-tab:hover {
  background-color: #e0e0e0;
}

/* web-frame样式 - 已删除
.web-frame {
  width: 100%;
  height: 100%;
  border: none;
}
*/

/* 输入区域样式已在bottom-bar.css中定义，此处保持注释 */
/*
.input-container {
  width: 100%;
}

#ai-prompt {
  width: 100%;
  padding: 8px 8px;
  border: 1px solid transparent;
  border-radius: 12px;
  resize: none;
  outline: none;
  height: 80px;
  font-family: inherit;
  font-size: 14px;
  color: #333;
  background-color: #f5f5f5;
  transition: background-color 0.3s, border-color 0.3s, box-shadow 0.3s;
}

#ai-prompt:focus {
  background-color: #f1f1f1;
  border-color: rgba(26, 115, 232, 0.4);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

#ai-prompt::placeholder {
  color: #999;
  font-size: 14px;
}
*/

/* 以下操作按钮样式已移至 bottom-bar.css，此处注释掉 */
/*
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.ai-button {
  background-color: #1a73e8;
  color: white;
  padding: 8px 24px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(26, 115, 232, 0.2);
  display: none;
}

.ai-button:hover {
  background-color: #1557b0;
  box-shadow: 0 3px 8px rgba(26, 115, 232, 0.3);
  transform: translateY(-1px);
}

.add-button {
  background-color: #34a853;
  color: white;
  padding: 8px 24px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(52, 168, 83, 0.2);
}

.add-button:hover {
  background-color: #2d8e47;
  box-shadow: 0 3px 8px rgba(52, 168, 83, 0.3);
  transform: translateY(-1px);
}
*/

/* 以下画布相关样式已移至 canvas.css，此处注释掉 */
/*
.canvas-container {
  flex: 1;
  background-color: #fcfcfc;
  position: relative;
  overflow: hidden;
}

#note-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f8f9fd;
  background-image: linear-gradient(
      rgba(200, 210, 230, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(200, 210, 230, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
  box-shadow: inset 0 0 100px rgba(220, 230, 250, 0.4);
}

.canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  pointer-events: none;
}

.bg-dots {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(26, 115, 232, 0.05);
  opacity: 0.5;
}

.bg-gradient-bubble {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.1;
  z-index: -1;
}
*/

/* 以下便签相关样式已移至 notes.css，此处注释掉 */
/*
.note {
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: box-shadow 0.2s;
  resize: both;
  min-width: 150px;
  min-height: 150px;
  max-width: 500px;
  max-height: 500px;
}

.note:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.note-header {
  height: 30px;
  padding: 0 2px 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(0, 0, 0, 0.05);
  cursor: move;
  user-select: none;
  z-index: 1;
}

.note-title {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.note-title.editing {
  cursor: text;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 3px;
  padding: 0 5px;
  min-width: 60px;
  white-space: nowrap;
  outline: none;
}

.note-close {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 5px;
}

.note-close:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: #e74c3c;
}

.note-body {
  position: relative;
  flex: 1;
  display: flex;
  overflow: hidden;
}

.note-content {
  width: 100%;
  height: 100%;
  padding: 10px 12px;
  border: none;
  outline: none;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.8);
  background-color: transparent;
  overflow-y: auto;
  scrollbar-width: none;
}

.note-content::-webkit-scrollbar {
  display: none;
}

.custom-scrollbar {
  position: absolute;
  top: 0;
  right: 0;
  width: 6px;
  height: 100%;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 2;
  pointer-events: none;
}

.note:hover .custom-scrollbar,
.note-content:focus ~ .custom-scrollbar {
  opacity: 1;
}

.scrollbar-thumb {
  position: absolute;
  right: 1px;
  width: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.note-resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  background-color: transparent;
  cursor: nwse-resize;
  border-radius: 0 0 4px 0;
  transition: opacity 0.2s;
  z-index: 3;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10'%3E%3Cpath fill='rgba(0,0,0,0.25)' d='M0 10L10 0L10 10'/%3E%3C/svg%3E");
  background-position: bottom right;
  background-repeat: no-repeat;
  opacity: 0.4;
}

.note-resize-handle:hover {
  background-color: transparent;
  opacity: 0.7;
}

.note-yellow {
  background-color: #fff8c5;
}
.note-blue {
  background-color: #d6eaf8;
}
.note-green {
  background-color: #d5f5e3;
}
.note-pink {
  background-color: #fdedec;
}
.note-purple {
  background-color: #e8daef;
}
*/

/* 以下消息显示样式已移至 utils.css，此处注释掉 */
/*
.error-message {
  position: fixed;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%) translateY(20px);
  background-color: #ff3333;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s, transform 0.3s;
  z-index: 1000;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 80%;
}

.error-message.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}
*/

/* 以下Markdown相关样式已移至 markdown.css，此处注释掉 */
/*
.markdown-preview {
  width: 100%;
  height: 100%;
  padding: 10px 12px;
  overflow-y: auto;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.8);
  background-color: transparent;
  display: none;
  scrollbar-width: none;
  cursor: text;
  position: relative;
  z-index: 1;
}

.markdown-preview:after {
  display: none;
}

.note:hover .markdown-preview:after {
  display: none;
}

.edit-hint {
  position: absolute !important;
  right: 10px !important;
  bottom: 10px !important;
  font-size: 11px !important;
  color: rgba(0, 0, 0, 0.5) !important;
  background-color: rgba(255, 255, 255, 0.7) !important;
  padding: 3px 8px !important;
  border-radius: 4px !important;
  opacity: 0;
  transition: opacity 0.3s, background-color 0.2s !important;
  pointer-events: none !important;
  z-index: 100 !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  user-select: none !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* 移除悬停时显示编辑提示的规则，由JavaScript控制 */
/* .note:hover .edit-hint {
  opacity: 0.9 !important;
} */

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
}

.markdown-preview h1 {
  font-size: 20px;
}

.markdown-preview h2 {
  font-size: 18px;
}

.markdown-preview h3 {
  font-size: 16px;
}

.markdown-preview p {
  margin-bottom: 10px;
}

.markdown-preview ul,
.markdown-preview ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.markdown-preview code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.markdown-preview pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 10px;
}

.markdown-preview pre code {
  background-color: transparent;
  padding: 0;
}

.markdown-preview blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.1);
  padding-left: 10px;
  margin-left: 0;
  color: rgba(0, 0, 0, 0.6);
}

.markdown-preview a {
  color: #1a73e8;
  text-decoration: none;
}

.markdown-preview a:hover {
  text-decoration: underline;
}

.markdown-preview img {
  max-width: 100%;
}

.markdown-preview table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 10px;
}

.markdown-preview table,
.markdown-preview th,
.markdown-preview td {
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.markdown-preview th,
.markdown-preview td {
  padding: 5px;
  text-align: left;
}
*/

/* 以下切换按钮样式已移至其他CSS文件，此处注释掉 */
/*
.note-toggle-button {
  display: none;
}
*/
