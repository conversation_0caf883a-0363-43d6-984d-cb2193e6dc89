/* 用户管理样式 */

/* 用户列表工具栏 */
.users-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

/* 搜索框容器 */
.users-search-container {
  position: relative;
  flex: 1;
  max-width: 300px;
}

/* 搜索框 */
.users-search {
  width: 100%;
  padding: 8px 12px 8px 32px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.users-search:focus {
  border-color: var(--primary-color, #1a73e8);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
  outline: none;
}

/* 搜索图标 */
.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary, #666);
  font-size: 14px;
}

/* 用户操作按钮区 */
.users-actions {
  display: flex;
  gap: 8px;
}

/* 工具栏按钮样式 */
.user-toolbar-button {
  padding: 6px 12px;
  font-size: 13px;
}

/* 用户表格容器 */
.users-table-container {
  position: relative;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 16px;
}

/* 用户表格 */
.users-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

/* 表头 */
.users-table thead {
  background-color: var(--bg-light, #f5f5f7);
}

.users-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary, #333);
  border-bottom: 1px solid var(--border-color, #ddd);
  white-space: nowrap;
}

/* 可排序的表头 */
.users-table th.sortable {
  cursor: pointer;
}

/* 表头内容容器 */
.th-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 排序图标 */
.sort-icon {
  width: 16px;
  height: 16px;
  margin-left: 4px;
  opacity: 0.3;
  transition: opacity 0.2s;
}

.sortable:hover .sort-icon {
  opacity: 0.7;
}

.sort-asc .sort-icon:after {
  content: "↑";
}

.sort-desc .sort-icon:after {
  content: "↓";
}

/* 表格行 */
.users-table tbody tr {
  border-bottom: 1px solid var(--border-color, #ddd);
  transition: background-color 0.2s;
}

.users-table tbody tr:last-child {
  border-bottom: none;
}

.users-table tbody tr:hover {
  background-color: var(--hover-bg, #f0f0f0);
}

/* 表格单元格 */
.users-table td {
  padding: 12px 16px;
  color: var(--text-primary, #333);
  vertical-align: middle;
}

/* 用户操作单元格 */
.users-table td.user-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 用户状态标签 */
.user-status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 500;
}

.status-active {
  background-color: rgba(46, 204, 113, 0.2);
  color: #27ae60;
}

.status-inactive {
  background-color: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.status-admin {
  background-color: rgba(52, 152, 219, 0.2);
  color: #2980b9;
}

/* 用户操作按钮 */
.user-action-button {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  cursor: pointer;
  border: 1px solid var(--border-color);
  background-color: var(--bg-light);
  transition: all 0.2s;
}

.user-action-button:hover {
  background-color: var(--hover-bg);
}

.user-action-button.reset-password {
  color: #3498db;
  border-color: rgba(52, 152, 219, 0.3);
}

.user-action-button.reset-password:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

.user-action-button.delete-user {
  color: #e74c3c;
  border-color: rgba(231, 76, 60, 0.3);
}

.user-action-button.delete-user:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

/* 用户统计 */
.user-stats {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 0 8px;
}

.stat-label {
  font-size: 0.9em;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 1.5em;
  font-weight: 600;
  color: var(--text-primary);
}

/* 空状态 */
.empty-state {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  text-align: center;
  color: var(--text-secondary, #666);
  background-color: var(--bg-primary, #fff);
  z-index: 1;
  gap: 12px;
}

/* 加载动画 */
.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color, #1a73e8);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 错误图标 */
.error-icon {
  font-size: 24px;
  color: #e74c3c;
  margin-bottom: 8px;
}

/* 重试按钮 */
.retry-button {
  padding: 6px 12px;
  background-color: var(--primary-color, #1a73e8);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #1557b0;
}

/* 分页控制 */
.users-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid var(--border-color, #ddd);
  font-size: 13px;
  color: var(--text-secondary, #666);
}

.pagination-info {
  flex: 1;
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

.pagination-button {
  padding: 4px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  background-color: var(--bg-light, #f5f5f7);
  color: var(--text-primary, #333);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--hover-bg, #e8e8ed);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 用户详情模态框 */
.user-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.user-detail-modal.active {
  opacity: 1;
  visibility: visible;
}

.user-detail-container {
  background-color: var(--bg-primary);
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.user-detail-content {
  padding: 16px;
}

.user-detail-footer {
  padding: 16px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .user-created {
    display: none;
  }

  .user-row {
    flex-wrap: wrap;
  }

  .user-name {
    flex: 1;
  }

  .user-status {
    flex: 1;
  }

  .user-actions {
    flex-basis: 100%;
    justify-content: flex-start;
    margin-top: 8px;
  }
}
