/**
 * 分享页面特定样式 - share.css
 *
 * 这个文件只包含分享页面特有的样式，如头部和悬浮提示等
 * 画布、便签和网格背景等样式已经从主页面导入
 */

/* 分享页面特有的样式覆盖 */
body {
  background-color: #f9f9f9;
  overflow: hidden;
}

/* 分享页面样式 */
.share-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fcfcfc;
}

.share-header {
  background-color: #f8f8f8;
  padding: 12px 24px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.share-title {
  font-size: 1.5rem;
  margin: 0;
  color: #333;
  font-weight: 600;
}

.share-info {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #666;
  align-items: center;
}

.share-id {
  font-family: monospace;
  background-color: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
}

.refresh-button {
  background-color: #4a6ee0;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background-color: #3a5ecc;
}

.refresh-button:active {
  background-color: #2a4ebc;
}

/* 移除旧的提示区域样式 */

/* 新的悬浮提示样式 */
.floating-tip {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: auto;
  max-width: 90%;
  animation: fadeInUp 0.5s ease-out;
}

.tip-content {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 12px 20px;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.tip-icon {
  margin-right: 10px;
  font-size: 16px;
}

.tip-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
  cursor: pointer;
  margin-left: 15px;
  padding: 0 5px;
  line-height: 1;
  transition: color 0.2s;
}

.tip-close:hover {
  color: white;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, 20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 分享页面只读模式的样式覆盖 - 仅包含功能性样式 */

/* 禁用便签调整大小功能 */
.note.read-only {
  resize: none !important; /* 禁用CSS的resize属性 */
}

/* 隐藏调整大小控件 */
.note.read-only .note-resize-handle {
  display: none !important;
  pointer-events: none !important;
}

/* 禁用关闭按钮交互 */
.note.read-only .note-close {
  pointer-events: none; /* 禁用交互 */
  opacity: 0.5; /* 降低透明度表示禁用状态 */
}

/* 确保预览区域始终可见 */
.note.read-only .markdown-preview {
  display: block !important;
}

/* 禁用标题编辑 */
.note.read-only .note-title {
  pointer-events: none;
}

/* 禁用便签拖动 */
.note.read-only .note-title-container {
  cursor: default;
}

/* Markdown样式使用原始页面的markdown.css */

/* 便签隐藏提示信息样式 */
.notes-hidden-info {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  transition: opacity 0.5s ease;
}

.info-content {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 12px 20px;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.info-icon {
  margin-right: 10px;
  font-size: 16px;
}

/* 添加加载指示器样式 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15px 25px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-size: 1rem;
  color: #333;
  display: flex;
  align-items: center;
  z-index: 1000;
}

.loading-indicator:before {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border: 3px solid #4a6ee0;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 错误消息样式 */
.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  padding: 25px 35px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  text-align: center;
  max-width: 80%;
}

.error-message h2 {
  color: #e53935;
  margin-top: 0;
  margin-bottom: 15px;
}

.error-message p {
  margin: 10px 0;
  color: #555;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .share-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 10px 16px;
  }

  .share-info {
    flex-direction: column;
    gap: 5px;
    width: 100%;
  }

  .share-id {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }
}
