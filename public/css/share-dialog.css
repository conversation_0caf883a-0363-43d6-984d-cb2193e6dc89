/* 分享对话框样式 */
.share-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.2s ease-out;
}

.share-dialog-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease-out;
}

.share-dialog h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
  font-size: 1.5rem;
}

/* 分享状态样式 */
.share-status {
  margin: 10px 0;
  font-size: 15px;
  color: #555;
}

.share-status-active {
  color: #4caf50;
  font-weight: 500;
}

.share-description {
  margin: 16px 0;
  color: #666;
  line-height: 1.5;
}

/* 主要按钮样式 */
.primary-btn {
  background-color: #4a6ee0;
  color: white;
  font-weight: 500;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primary-btn:hover {
  background-color: #3a5ecc;
}

.primary-btn:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

/* 对话框按钮区域样式 */
.share-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

/* 画布名称容器样式 */
.canvas-name-container {
  margin: 16px 0;
}

.canvas-name-container label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.canvas-name {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #555;
  margin-bottom: 16px;
}

.canvas-name:focus {
  border-color: #4a6ee0;
  outline: none;
  box-shadow: 0 0 0 2px rgba(74, 110, 224, 0.2);
}

.share-url-container {
  display: flex;
  margin: 16px 0;
}

.share-url {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 14px;
  color: #555;
}

.copy-btn {
  padding: 10px 16px;
  background-color: #4a6ee0;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.copy-btn:hover {
  background-color: #3a5ecc;
}

.share-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

/* 分享选项样式 */
.share-options {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 8px;
  border-left: 4px solid #4a6ee0;
}

.share-option-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.share-info-text {
  margin: 8px 0;
  font-size: 13px;
  color: #666;
  font-style: italic;
}

.refresh-share-btn {
  background-color: #4a6ee0;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-share-btn:hover {
  background-color: #3a5ecc;
}

.refresh-share-btn:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.close-share-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.close-share-btn:hover {
  background-color: #c0392b;
}

.close-share-btn:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.close-share-info {
  margin-top: 8px;
  font-size: 13px;
  color: #777;
  font-style: italic;
}

.share-id {
  font-family: monospace;
  color: #333;
  font-weight: 500;
}

.share-actions,
.share-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.open-btn,
.close-btn {
  padding: 10px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.open-btn {
  background-color: #4a6ee0;
  color: white;
  border: none;
}

.open-btn:hover {
  background-color: #3a5ecc;
}

.close-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.close-btn:hover {
  background-color: #e8e8e8;
}

/* 移除旧的消息提示样式，使用统一的通知管理器 */

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 600px) {
  .share-dialog-content {
    padding: 16px;
  }

  .share-actions {
    flex-direction: column;
  }

  .open-btn,
  .close-btn {
    width: 100%;
  }
}
