/**
 * 字体优化样式表 - font-optimization.css
 * 
 * 本文件专门处理字体渲染优化，确保在不同分辨率和设备上
 * 文本都能清晰显示。包含以下优化：
 * 
 * 1. 全局字体平滑处理
 * 2. 高DPI屏幕适配
 * 3. 文本渲染质量优化
 * 4. 字体特性支持
 */

/* 全局字体渲染优化 */
*,
*::before,
*::after {
  /* 字体平滑处理 - 改善在Retina和高DPI屏幕上的显示效果 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 文本渲染优化 - 提高文本质量 */
  text-rendering: optimizeLegibility;

  /* 字体特性支持 - 启用连字和字距调整 */
  font-feature-settings: "liga" 1, "kern" 1;
}

/* 确保所有输入框和文本区域都使用优化的字体渲染 */
input,
textarea,
select,
button {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 针对小尺寸文字的特别优化 */
.small-text,
.note-title,
.ui-text {
  /* 对于小字体，使用更精确的字体平滑 */
  -webkit-font-smoothing: subpixel-antialiased;
  font-variant-ligatures: none; /* 小字体不使用连字 */
}

/* 针对高对比度需求的优化 */
.high-contrast {
  color: rgba(0, 0, 0, 0.95) !important;
  font-weight: 500;
}

/* 媒体查询：针对高DPI屏幕的特殊优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* 在高DPI屏幕上使用更细致的字体平滑 */
  body,
  .note-content,
  .markdown-preview {
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeQuality;
  }

  /* 适当增加小字体的粗细度以提高清晰度 */
  .note-title,
  .small-text {
    font-weight: 500;
    letter-spacing: 0.01em;
  }
}

/* 媒体查询：针对低DPI屏幕的优化 */
@media (-webkit-max-device-pixel-ratio: 1), (max-resolution: 96dpi) {
  /* 在低DPI屏幕上使用子像素渲染 */
  body,
  .note-content,
  .markdown-preview {
    -webkit-font-smoothing: subpixel-antialiased;
  }
}

/* 暗色主题下的字体优化（为未来扩展准备） */
@media (prefers-color-scheme: dark) {
  body,
  .note-content,
  .markdown-preview {
    /* 暗色背景下通常需要更强的字体平滑 */
    -webkit-font-smoothing: antialiased;
    font-weight: 400; /* 稍微增加字体重量以提高可读性 */
  }
}

/* 减少动画期间的字体渲染问题 */
.animating {
  text-rendering: auto; /* 动画期间使用快速渲染 */
}

/* 打印时的字体优化 */
@media print {
  * {
    -webkit-font-smoothing: auto;
    -moz-osx-font-smoothing: auto;
    text-rendering: optimizeSpeed; /* 打印时优先速度 */
  }
}
