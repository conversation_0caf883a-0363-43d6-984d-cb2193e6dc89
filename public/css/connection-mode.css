/**
 * 连接模式样式 - connection-mode.css
 *
 * 包含便签连接模式相关的样式，包括模式切换开关
 */

/* 模式切换容器 */
.mode-toggle-container {
  display: none; /* 默认隐藏 */
  align-items: center;
  margin-left: auto; /* 推到右侧 */
  margin-right: 10px; /* 与清空连接按钮保持间距 */
  height: 24px;
  position: relative;
}

/* 模式切换文本 */
.mode-toggle-text {
  font-size: 11px;
  color: #555;
  margin-left: 6px;
  white-space: nowrap;
  font-weight: 500;
}

/* 隐藏原始复选框 */
.mode-toggle-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* 切换开关轨道 - 使用标准样式 */
.mode-toggle-track {
  display: inline-block;
  width: 34px;
  height: 20px;
  border-radius: 34px;
  background-color: rgba(234, 67, 53, 0.7); /* 替换模式为红色 */
  position: relative;
  transition: all 0.2s ease;
  box-shadow: none; /* 确保没有阴影 */
}

/* 切换开关滑块 - 使用标准样式并确保居中 */
.mode-toggle-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

/* 选中状态的轨道 */
.mode-toggle-input:checked + .mode-toggle-label .mode-toggle-track {
  background-color: rgba(52, 168, 83, 0.8); /* 汇总模式为绿色 */
}

/* 选中状态的滑块 */
.mode-toggle-input:checked + .mode-toggle-label .mode-toggle-thumb {
  transform: translateX(14px); /* 调整位移距离，确保在右侧位置对齐 */
}

/* 切换开关标签 */
.mode-toggle-label {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  vertical-align: middle;
}

/* 鼠标悬停效果 */
.mode-toggle-label:hover .mode-toggle-track {
  background-color: rgba(234, 67, 53, 0.8); /* 替换模式悬停时加深红色 */
}

.mode-toggle-input:checked + .mode-toggle-label:hover .mode-toggle-track {
  background-color: rgba(52, 168, 83, 0.9); /* 汇总模式悬停时加深绿色 */
}

/* 完全移除所有聚焦状态阴影 */
.mode-toggle-input:focus + .mode-toggle-label .mode-toggle-track {
  box-shadow: none;
}

.mode-toggle-input:checked:focus + .mode-toggle-label .mode-toggle-track {
  box-shadow: none;
}

/* 删除旧的tooltip样式，现在使用全局tooltip系统 */

/* 便签到便签连接线样式 */
.note-to-note-connection {
  /* 确保便签到便签的连接线在普通连接线之上 */
  z-index: 10 !important;
}

/* 连接线容器样式 */
.connection-line-container {
  /* 普通连接线的z-index */
  z-index: 5;
}

/* CSS变量定义 - 连接线颜色和样式 */
:root {
  --note-to-note-connection-color: rgba(100, 200, 100, 0.7);
  --note-to-note-connection-size: 2;
  --note-connection-color: rgba(110, 150, 190, 0.55);
  --note-connection-size: 1.5;
  --note-connection-highlight-color: rgba(249, 132, 121, 0.65);
  --note-connection-highlight-size: 2;
}

/* 清空连接按钮样式 */
.clear-all-connections {
  background: rgba(234, 67, 53, 0.1);
  border: 1px solid rgba(234, 67, 53, 0.3);
  color: #ea4335;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.clear-all-connections:hover {
  background: rgba(234, 67, 53, 0.2);
  border-color: rgba(234, 67, 53, 0.5);
}

/* 汇总模式优化样式 */
.slots-container.visible {
  /* 确保插槽容器在汇总模式下正确显示 */
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease;
}

/* 便签选中状态样式 */
.note.selected {
  box-shadow: 0 0 0 2px rgba(52, 168, 83, 0.8);
  transform: scale(1.02);
  transition: all 0.2s ease;
}
