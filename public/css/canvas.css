/**
 * 画布与缩放控制样式 - canvas.css
 *
 * 这个文件包含了无限便签应用中画布区域的所有样式定义，主要包括：
 * 1. 画布容器与背景样式设置
 * 2. 网格背景与装饰元素样式
 * 3. 便签容器定位与事件处理
 * 4. 缩放控制器的外观与交互样式
 *
 * 这些样式共同构建了一个可无限缩放、平移的便签工作区，
 * 并提供了直观的用户界面控制元素。
 */

/* 画布容器基础样式 - 作为整个应用的工作区 */
.canvas-container {
  position: relative; /* 添加相对定位，为绝对定位的子元素提供参考 */
  flex: 1; /* 占据父容器中的剩余空间 */
  background-color: #f8f9fd; /* 更柔和的浅蓝色背景 */
  position: relative; /* 建立定位上下文，便于子元素绝对定位 */
  overflow: hidden; /* 隐藏超出容器边界的内容 */
  width: 100%;
  height: 100%;
  cursor: grab;
  box-shadow: inset 0 0 100px rgba(220, 230, 250, 0.4); /* 添加内阴影，创造柔和的光晕效果 */
}

/* 画布元素样式 - 实际的工作区背景 */
#note-canvas {
  width: 100%; /* 宽度占满容器 */
  height: 100%; /* 高度占满容器 */
  position: absolute; /* 创建定位上下文 */
  top: 0;
  left: 0;
  overflow: hidden;
}

/* 网格背景容器 */
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  pointer-events: none; /* 让事件穿透到下层 */
}

/* 装饰性背景容器 */
.canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none; /* 允许点击穿透 */
  overflow: hidden;
}

/* 实际网格 - 现代化设计 */
.grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(
      rgba(65, 90, 160, 0.03) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(65, 90, 160, 0.03) 1px, transparent 1px),
    linear-gradient(rgba(65, 90, 160, 0.02) 0.5px, transparent 0.5px),
    linear-gradient(90deg, rgba(65, 90, 160, 0.02) 0.5px, transparent 0.5px);
  background-size: 30px 30px, 30px 30px, 6px 6px, 6px 6px;
  z-index: 0;
  will-change: transform; /* 性能优化 */
  transition: none; /* 移除过渡效果 */
}

/* 装饰性背景容器 - 用于放置视觉元素 */
.canvas-background {
  position: absolute; /* 绝对定位，脱离文档流 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0; /* 设置层级为0，确保在内容下方 */
  overflow: hidden; /* 隐藏超出部分 */
  pointer-events: none; /* 禁用鼠标事件，确保不影响用户交互 */
}

/* 点阵装饰元素样式 - 创建背景点缀效果 */
.bg-dots {
  position: absolute; /* 绝对定位，方便任意位置放置 */
  width: 6px; /* 设置点的宽度 */
  height: 6px; /* 设置点的高度 */
  border-radius: 50%; /* 圆形边框 */
  background-color: rgba(26, 115, 232, 0.05); /* 淡蓝色，透明度低 */
  opacity: 0.5; /* 设置透明度 */
  box-shadow: 0 0 10px rgba(26, 115, 232, 0.1); /* 添加柔和的光晕 */
  transition: opacity 2s ease; /* 添加缓慢的透明度变化效果 */
  animation: float-dots 20s infinite linear; /* 添加缓慢浮动动画 */
}

/* 渐变气泡装饰 - 添加柔和的背景色块 */
.bg-gradient-bubble {
  position: absolute; /* 绝对定位，自由定位 */
  border-radius: 50%; /* 圆形边框 */
  filter: blur(60px); /* 添加模糊效果 */
  opacity: 0.1; /* 低透明度，不干扰主要内容 */
  z-index: -1; /* 置于最底层 */
  animation: pulse-bubble 15s infinite alternate ease-in-out; /* 添加脉动效果 */
}

/* 装饰线条样式 */
.bg-line {
  position: absolute;
  border-radius: 4px;
  box-shadow: 0 0 15px rgba(26, 115, 232, 0.1);
  transform-origin: center;
  animation: float-line 25s infinite alternate ease-in-out;
  z-index: -1;
}

/* 点阵浮动动画 */
@keyframes float-dots {
  0% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(10px) translateX(5px);
  }
  50% {
    transform: translateY(0) translateX(10px);
  }
  75% {
    transform: translateY(-10px) translateX(5px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

/* 线条浮动动画 */
@keyframes float-line {
  0% {
    transform: rotate(var(--rotation, 0deg)) translateY(0);
  }
  50% {
    transform: rotate(var(--rotation, 0deg)) translateY(15px);
  }
  100% {
    transform: rotate(var(--rotation, 0deg)) translateY(0);
  }
}

/* 气泡脉动动画 */
@keyframes pulse-bubble {
  0% {
    transform: scale(1);
    opacity: 0.05;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.05;
  }
}

/* 便签容器样式 - 所有便签的直接父容器 */
#note-container {
  position: absolute; /* 绝对定位，可随画布移动 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: none; /* 移除过渡效果，确保即时响应 */
  pointer-events: none; /* 默认不接收鼠标事件，传递给画布 */
  will-change: transform; /* 性能优化，提前告知浏览器此元素会变换 */
  transform-style: flat; /* 性能优化，不创建新的渲染上下文 */
}

/* 便签容器 */
.note-container {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  will-change: transform; /* 性能优化 */
  transition: none; /* 移除过渡效果 */
}

/* 单个便签样式 */
#note-container .note {
  pointer-events: auto; /* 便签本身需要接收鼠标事件 */
}

/* 缩放控制面板样式 - 磨玉璃效果 */
.zoom-controls {
  position: absolute; /* 绝对定位 */
  top: 20px; /* 从底部改为顶部 */
  right: 20px; /* 保持右侧位置 */
  background-color: rgba(255, 255, 255, 0.6); /* 半透明背景 */
  backdrop-filter: blur(10px); /* 磨玉璃效果 */
  -webkit-backdrop-filter: blur(10px); /* Safari 兼容性 */
  border-radius: 20px; /* 圆角边框 */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 柔和阴影 */
  display: flex; /* 弹性布局 */
  flex-direction: column; /* 纵向排列 */
  align-items: center; /* 居中对齐 */
  padding: 6px 8px; /* 内边距 */
  z-index: 1000; /* 高层级，确保显示在最上层 */
  border: 1px solid rgba(255, 255, 255, 0.7); /* 半透明白色边框 */
  width: 44px; /* 宽度 */
  box-sizing: border-box; /* 确保边框和内边距包含在宽度内 */
}

/* 缩放按钮的基本样式 */
.zoom-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  color: #333;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1px 0;
  padding: 0;
  transition: all 0.2s;
  line-height: 1;
  box-sizing: border-box;
}

/* 缩放按钮悬停状态样式 */
.zoom-btn:hover {
  background-color: rgba(
    255,
    255,
    255,
    0.5
  ); /* 半透明白色背景，与磨玉璃效果协调 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* 轻微阴影提供立体感 */
}

/* 缩放比例显示区域样式 */
.zoom-display {
  margin: 2px 0; /* 进一步减少上下间距 */
  font-size: 10px; /* 缩小字体大小 */
  color: #666; /* 文字颜色 */
  font-weight: 500; /* 字体粗细 */
  width: 28px; /* 与按钮宽度一致 */
  text-align: center; /* 文字居中对齐 */
  line-height: 1; /* 保持垂直空间紧凑 */
}

/* 重置缩放按钮的特殊样式 */
.zoom-reset {
  font-size: 14px; /* 进一步减小重置按钮文字大小 */
}

/* 便签合集按钮样式 */
.read-mode-btn {
  padding: 6px; /* 增加内边距，让图标居中且更小 */
}

/* 分享按钮样式 */
.share-btn {
  padding: 6px; /* 增加内边距，让图标居中且更小 */
}

/* 所有按钮内的SVG图标样式 */
.zoom-btn svg {
  width: 16px;
  height: 16px;
  display: block;
  stroke: #444; /* 稍深的图标颜色 */
}

/* 所有按钮悬停时图标颜色变化 */
.zoom-btn:hover svg {
  stroke: #1a73e8; /* 悬停时图标变为蓝色 */
}

/* 设置按钮样式 - 与其他按钮区分开来 */
.settings-btn {
  background-color: rgba(26, 115, 232, 0.15); /* 淡蓝色背景，区分于其他按钮 */
  border: 1px solid rgba(26, 115, 232, 0.3); /* 淡蓝色边框 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* 稍强的阴影 */
  margin-bottom: 8px; /* 增加与下方按钮的间距 */
  padding: 6px; /* 与其他按钮保持一致的内边距 */
}

/* 设置按钮的SVG图标颜色 */
.settings-btn svg {
  stroke: #1a73e8; /* 蓝色图标，区分于其他按钮 */
}

.settings-btn:hover {
  background-color: rgba(26, 115, 232, 0.25); /* 悬停时背景色变浓 */
  box-shadow: 0 2px 5px rgba(26, 115, 232, 0.15); /* 增强阴影，也带有蓝色 */
}

.settings-btn:active {
  background-color: rgba(26, 115, 232, 0.35); /* 点击时背景色更浓 */
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); /* 内陷效果 */
  transform: translateY(1px); /* 轻微下沉 */
}
