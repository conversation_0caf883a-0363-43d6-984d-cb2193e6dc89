/**
 * 底部控制栏样式表 - bottom-bar.css
 *
 * 本文件定义了应用底部控制栏的所有样式，包括：
 * 1. 底部栏容器：固定定位、边框和阴影效果
 * 2. 输入区域：文本输入框及其各种状态样式
 * 3. 操作按钮：AI生成和添加便签按钮
 * 4. 辅助元素：AI模型标识
 * 5. 折叠/展开控制
 */

/* 底部控制栏基础样式 */
.bottom-bar {
  position: fixed;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column-reverse; /* 反转方向，状态栏在顶部，内容区在底部 */
  background-color: #ffffff;
  border-radius: 18px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  width: 500px;
  max-width: 85%;
  border: 1px solid rgba(232, 232, 232, 0.8);
  overflow: hidden;
  z-index: 9999;
  transition: height 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
}

/* 添加悬停效果，微妙提升 */
.bottom-bar:hover {
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
}

/* 底部栏内容区 */
.bottom-bar-content {
  padding: 10px;
  transition: height 0.3s ease, opacity 0.3s ease, max-height 0.3s ease;
  max-height: 60px;
}

/* 新增：底部栏布局容器，用于水平排列输入框和按钮 */
.bottom-bar-layout {
  display: flex;
  align-items: center;
  gap: 8px; /* 输入框和按钮之间的间距 */
}

/* 输入容器及输入框 */
.input-container {
  flex: 1; /* 让输入框容器占据剩余空间 */
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  margin-right: 0; /* 确保容器不会有右侧外边距 */
  box-sizing: border-box; /* 确保边框不会增加宽度 */
}

#ai-prompt {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid transparent;
  border-radius: 12px;
  resize: none;
  outline: none;
  height: 36px; /* 减小高度 */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 12px;
  color: #333;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

#ai-prompt:focus {
  background-color: #f1f1f1;
  border-color: rgba(26, 115, 232, 0.4);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

#ai-prompt::placeholder {
  color: #999;
  font-size: 12px; /* 从14px减小到12px */
}

/* 自定义滚动条样式 */
#ai-prompt::-webkit-scrollbar {
  width: 6px; /* 滚动条宽度 */
}

#ai-prompt::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2); /* 滚动条颜色 */
  border-radius: 3px; /* 圆角 */
}

#ai-prompt::-webkit-scrollbar-track {
  background: transparent; /* 透明轨道背景 */
}

/* 操作按钮区域 - 现在与输入框同级 */
.action-buttons {
  display: flex;
  gap: 8px; /* 按钮之间的间距 */
  align-items: center;
}

/* 样式图标 */
.icon-add,
.icon-ai {
  font-size: 16px;
  display: inline-block;
}

.icon-add:before {
  content: "+";
}

.icon-ai:before {
  content: "AI";
  font-size: 12px;
  font-weight: bold;
}

/* 添加便签按钮 */
.add-button {
  background-color: #34a853;
  color: white;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 18px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.add-button:hover {
  background-color: #2d8e47;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* AI生成按钮 */
.ai-button {
  background-color: #1a73e8;
  color: white;
  height: 28px;
  width: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: none; /* 默认隐藏AI按钮 */
}

.ai-button:hover {
  background-color: #1557b0;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* 便签连接模式的AI生成按钮样式 */
.ai-button.connected-mode {
  background-color: #34a853; /* 改用绿色，与连接便签的颜色统一 */
  position: relative;
  overflow: visible;
}

.ai-button.connected-mode:hover {
  background-color: #2d8e47; /* 深一点的绿色 */
}

/* 便签连接模式的指示标记 */
.ai-button.connected-mode::after {
  content: "";
  position: absolute;
  top: -3px;
  right: -3px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ff5722; /* 醒目的橙色指示器 */
  border: 1px solid white;
}

/* 连接模式下图标不变，内容添加小连接标识 */
.ai-button.connected-mode .icon-ai:before {
  content: "AI";
  font-size: 10px; /* 缩小字体以容纳其他元素 */
}

.ai-button:disabled {
  background-color: #b0bec5;
  color: #e0e0e0;
  cursor: not-allowed;
  box-shadow: none;
  pointer-events: none;
}

/* 生成中的动画效果 - 脉冲扩散 */
.ai-button.generating::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ai-pulse 1.5s infinite ease-in-out;
}

/* 生成中的按钮变为取消按钮 */
.ai-button.generating {
  background-color: #ea4335; /* 红色背景 */
}

.ai-button.generating:hover {
  background-color: #d32f2f; /* 深红色 */
}

/* 生成中的按钮图标变为取消图标 */
.ai-button.generating .icon-ai:before {
  content: "×"; /* 显示取消符号 */
  font-size: 18px;
  font-weight: bold;
}

/* 脉冲动画关键帧 */
@keyframes ai-pulse {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 40px;
    height: 40px;
    opacity: 0;
  }
}

/* 旋转动画关键帧 */
@keyframes ai-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 加载旋转动画 */
@keyframes ai-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 底部状态栏 */
.bottom-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee; /* 从top改为bottom */
  font-size: 12px;
  color: #666;
  height: 30px; /* 固定高度 */
}

/* AI模型指示器 */
.ai-model-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 5px;
  height: 100%;
}

.ai-model-icon {
  background-color: #e1f5fe;
  color: #0288d1;
  font-size: 10px;
  font-weight: bold;
  padding: 0;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5px 0;
}

.ai-model {
  color: #666;
  font-size: 12px;
}

/* 连接状态指示器 - 隐藏 */
.connection-status-indicator {
  display: none; /* 完全隐藏连接状态指示器 */
}

/* 折叠/展开控制按钮 */
.toggle-bar-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #888;
  padding: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background-color: #f0f0f0;
  margin: 5px 5px 5px 0;
}

.toggle-bar-button:hover {
  background-color: #e0e0e0;
  color: #333;
}

.icon-chevron-up {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.icon-chevron-up:before {
  content: "▼"; /* 默认向下箭头，表示可以展开 */
  font-size: 10px;
  line-height: 1;
  display: block;
  text-align: center;
  position: relative;
  top: 1px; /* 微调位置，使其在视觉上更居中 */
}

/* 折叠状态类 - 修改为向上展开的逻辑 */
.bottom-bar.collapsed .bottom-bar-content {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
  overflow: hidden;
}

.bottom-bar.collapsed .toggle-bar-button .icon-chevron-up:before {
  content: "▲"; /* 折叠状态显示向上箭头，表示点击可展开 */
  font-size: 10px;
  line-height: 1;
  display: block;
  text-align: center;
  position: relative;
  top: 1px; /* 微调位置，使其在视觉上更居中 */
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .bottom-bar {
    width: 90%;
  }
}

@media screen and (max-width: 480px) {
  .bottom-bar {
    width: 95%;
  }

  #ai-prompt {
    height: 32px;
    font-size: 13px;
  }
}
