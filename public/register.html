<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InfinityNotes - 注册</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="css/register.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <meta http-equiv="Content-Security-Policy"
        content="default-src 'self'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com;">
</head>

<body>
    <div class="login-container">
        <div class="login-banner">
            <div class="banner-grid"></div>
            <div class="banner-decor banner-decor-1"></div>
            <div class="banner-decor banner-decor-2"></div>
            <div class="banner-decor banner-decor-3"></div>

            <div class="brand">
                <img src="img/logo.png" alt="Logo" class="banner-logo">
                <span>InfinityNotes - 无限便签</span>
            </div>
            <div class="banner-content">
                <h1>智能创作<br>无限可能</h1>
                <p>让AI助力你的创意，轻松将想法转化为精美便签</p>
            </div>
        </div>
        <div class="login-form-container">
            <div class="login-form-wrapper">
                <div class="login-header">
                    <h2>创建账户</h2>
                    <p>请填写以下信息完成注册</p>
                </div>

                <div class="login-form">
                    <div class="message-container" id="register-message"></div>

                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" placeholder="请输入用户名" autocomplete="username">
                    </div>

                    <div id="password-container">
                        <!-- 密码输入组件将在这里动态创建 -->
                    </div>

                    <div id="confirm-password-container">
                        <!-- 确认密码输入组件将在这里动态创建 -->
                    </div>

                    <div class="form-group">
                        <label for="invite-code">邀请码</label>
                        <input type="text" id="invite-code" name="invite-code" placeholder="请输入邀请码">
                    </div>

                    <button type="button" id="register-button" class="login-button">注册</button>

                    <div class="login-options">
                        <div class="login-prompt">
                            <p>已有账户？ <a href="/login.html">立即登录</a></p>
                        </div>
                    </div>

                    <div class="login-footer">
                        <p>InfinityNotes - 无限便签 · 版本 1.0.0</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/register.js" type="module"></script>
</body>

</html>