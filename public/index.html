<!-- 声明文档类型为HTML5 -->
<!DOCTYPE html>
<!-- 定义HTML文档，并设置语言为简体中文 -->
<html lang="zh-CN">

<head>
    <!-- 设置文档字符编码为UTF-8，支持中文显示 -->
    <meta charset="UTF-8">
    <!-- 设置viewport以优化移动端显示 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 网页标题 -->
    <title>InfinityNotes - 无限便签</title>
    <!-- 引入外部CSS样式文件，更新为模块化CSS -->
    <link rel="stylesheet" href="css/main.css">
    <!-- 字体优化样式 - 改善文本在不同分辨率下的显示效果 -->
    <link rel="stylesheet" href="css/font-optimization.css">
    <!-- 引入自定义样式，用于优化界面 -->
    <link rel="stylesheet" href="css/custom-styles.css">
    <!-- 引入邀请码管理样式 -->
    <link rel="stylesheet" href="css/invite-code.css">
    <!-- 引入分享对话框样式 -->
    <link rel="stylesheet" href="css/share-dialog.css">
    <!-- 引入用户管理样式 -->
    <link rel="stylesheet" href="css/user-management.css">
    <!-- 添加连接模式样式 -->
    <link rel="stylesheet" href="css/connection-mode.css">
    <!-- 添加全局tooltip样式 -->
    <link rel="stylesheet" href="css/global-tooltip.css">
    <!-- 添加 marked.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@3.0.8/marked.min.js"></script>
    <!-- 添加 highlight.js 用于代码高亮（可选） -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.3.1/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.3.1/highlight.min.js"></script>
    <!-- 添加 leader-line 库用于便签连接线 -->
    <script src="https://cdn.jsdelivr.net/npm/leader-line/leader-line.min.js"></script>
</head>

<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 画布容器区域 -->
        <div class="canvas-container">
            <!-- 便签展示的画布区域 -->
            <div id="note-canvas">
                <!-- 背景元素将通过JS动态添加 -->
            </div>
        </div>

        <!-- 连接线容器 -->
        <div class="connections-container" id="connections-container">
            <!-- 连接线将通过SVG动态添加 -->
            <svg id="connections-svg" width="100%" height="100%"></svg>
        </div>

        <!-- 便签插槽区域 -->
        <div class="slots-container" id="slots-container">
            <!-- 标题 -->
            <div class="slots-title">已连接便签</div>

            <!-- 插槽列表 -->
            <div class="slots-list" id="slots-list">
                <!-- 插槽将通过JavaScript动态添加 -->
            </div>

            <!-- 连接模式切换开关 - 将tooltip移动到文本上 -->
            <div class="mode-toggle-container" id="connection-mode-selector">
                <input type="checkbox" id="mode-toggle" class="mode-toggle-input" checked>
                <label for="mode-toggle" class="mode-toggle-label">
                    <span class="mode-toggle-track"></span>
                    <span class="mode-toggle-thumb"></span>
                </label>
                <span class="mode-toggle-text"
                    data-tooltip="汇总模式：保留原始便签，并自动将它们连接到新便签<br>替换模式：删除原始便签，只保留新生成的便签">汇总模式</span>
            </div>

            <!-- 清除所有连接按钮 -->
            <button class="clear-all-connections" id="clear-all-connections">清空连接</button>
        </div>

        <!-- 底部控制栏 - 优化为更紧凑的设计 -->
        <div class="bottom-bar">
            <div class="bottom-bar-content">
                <!-- 重新组织结构，将输入区和按钮区并列放置 -->
                <div class="bottom-bar-layout">
                    <!-- 输入区域 -->
                    <div class="input-container">
                        <textarea id="ai-prompt" placeholder="输入提示或直接添加便签..."></textarea>
                    </div>

                    <!-- 操作按钮区域 - 现在与输入框同级 -->
                    <div class="action-buttons">
                        <button id="add-note" class="add-button" title="添加空白便签">
                            <i class="icon-add"></i>
                        </button>
                        <button id="ai-generate" class="ai-button" title="AI生成便签">
                            <i class="icon-ai"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 底部状态栏 -->
            <div class="bottom-status-bar">
                <!-- AI模型标识 - 更加紧凑 -->
                <div class="ai-model-indicator">
                    <span class="ai-model-icon">AI</span>
                    <span class="ai-model">deepseek-chat</span>
                </div>

                <!-- 折叠/展开控制按钮 -->
                <button class="toggle-bar-button" title="折叠/展开输入区域">
                    <i class="icon-chevron-up"></i>
                </button>
            </div>
        </div>

        <!-- 设置弹窗 -->
        <div class="settings-modal" id="settings-modal">
            <div class="settings-container">
                <div class="settings-header">
                    <h2>设置</h2>
                    <button class="close-settings zoom-btn settings-btn" id="close-settings">&times;</button>
                </div>

                <div class="settings-content">
                    <!-- 左侧导航 - 重新排序选项卡 -->
                    <div class="settings-nav">
                        <button class="nav-item active" data-tab="ai">AI 设置</button>
                        <button class="nav-item" data-tab="profile">个人中心</button>
                        <button class="nav-item admin-only" data-tab="users">用户管理</button>
                        <button class="nav-item" data-tab="backup">备份恢复</button>
                        <button class="nav-item" data-tab="appearance">外观</button>
                        <button class="nav-item" data-tab="about">关于</button>
                    </div>

                    <!-- 右侧设置项 -->
                    <div class="settings-panels">
                        <!-- AI设置 -->
                        <div class="settings-panel active" id="ai-panel">
                            <h3>AI配置</h3>
                            <div class="settings-panel-content">
                                <!-- API连接配置卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>API 连接配置</h4>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>基础 URL</label>
                                            <span class="settings-description">API 服务的基础 URL 地址</span>
                                        </div>
                                        <div class="settings-control">
                                            <div class="history-input-container">
                                                <input type="text" id="ai-base-url" class="settings-input"
                                                    placeholder="https://api.openai.com/v1">
                                                <div class="history-dropdown" id="base-url-history-dropdown">
                                                    <div class="history-dropdown-content">
                                                        <!-- 基础 URL 历史记录将通过 JavaScript 动态添加 -->
                                                        <div class="history-item-loading">加载中...</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>API 密钥</label>
                                            <span class="settings-description">您的 API 密钥将安全地存储在服务器端</span>
                                        </div>
                                        <div class="settings-control api-key-container">
                                            <div class="history-input-container">
                                                <input type="password" id="ai-api-key" class="settings-input"
                                                    placeholder="输入您的 API 密钥">
                                                <button type="button" id="toggle-api-key" class="toggle-visibility"
                                                    data-state="hidden" title="显示/隐藏密钥">
                                                    <span class="eye-icon"></span>
                                                </button>
                                                <div class="history-dropdown" id="api-key-history-dropdown">
                                                    <div class="history-dropdown-content">
                                                        <!-- API 密钥历史记录将通过 JavaScript 动态添加 -->
                                                        <div class="history-item-loading">加载中...</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 模型参数设置卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>模型参数设置</h4>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>AI 模型</label>
                                            <span class="settings-description">选择适合您的AI模型，不同模型具有不同的能力和价格</span>
                                        </div>
                                        <div class="settings-control">
                                            <div class="history-input-container">
                                                <input type="text" id="ai-model" class="settings-input"
                                                    placeholder="例如: gpt-3.5-turbo, gpt-4-turbo">
                                                <div class="history-dropdown" id="model-history-dropdown">
                                                    <div class="history-dropdown-content">
                                                        <!-- 模型历史记录将通过 JavaScript 动态添加 -->
                                                        <div class="history-item-loading">加载中...</div>
                                                    </div>
                                                </div>
                                                <div class="custom-select-dropdown" id="model-dropdown">
                                                    <div class="model-group-label">标准模型</div>
                                                    <div class="select-option" data-value="gpt-3.5-turbo">gpt-3.5-turbo
                                                    </div>
                                                    <div class="select-option" data-value="gpt-4">gpt-4</div>
                                                    <div class="select-option" data-value="gpt-4-turbo">gpt-4-turbo
                                                    </div>
                                                    <div class="select-option" data-value="deepseek-chat">deepseek-chat
                                                    </div>
                                                    <div class="select-option" data-value="claude-2">claude-2</div>
                                                    <div class="select-option" data-value="claude-instant">
                                                        claude-instant</div>

                                                    <div class="model-group-label">OpenRouter模型 (免费)</div>
                                                    <div class="select-option"
                                                        data-value="anthropic/claude-3-haiku:free">
                                                        anthropic/claude-3-haiku:free</div>
                                                    <div class="select-option" data-value="google/gemini-1.5-pro:free">
                                                        google/gemini-1.5-pro:free</div>
                                                    <div class="select-option"
                                                        data-value="mistralai/mistral-7b-instruct:free">
                                                        mistralai/mistral-7b-instruct:free</div>
                                                    <div class="select-option"
                                                        data-value="meta-llama/llama-3-8b-instruct:free">
                                                        meta-llama/llama-3-8b-instruct:free</div>

                                                    <div class="model-group-label">OpenRouter模型 (付费)</div>
                                                    <div class="select-option" data-value="anthropic/claude-3-opus">
                                                        anthropic/claude-3-opus</div>
                                                    <div class="select-option" data-value="anthropic/claude-3-sonnet">
                                                        anthropic/claude-3-sonnet</div>
                                                    <div class="select-option" data-value="google/gemini-1.5-flash">
                                                        google/gemini-1.5-flash</div>
                                                    <div class="select-option" data-value="openai/gpt-4o">openai/gpt-4o
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>最大令牌数</label>
                                            <span class="settings-description">控制生成便签内容的最大长度，较大的值会产生更长的回复</span>
                                        </div>
                                        <div class="settings-control">
                                            <input type="number" id="ai-max-tokens" class="settings-input" min="100"
                                                max="4000" step="50" value="800">
                                        </div>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>创意程度</label>
                                            <span class="settings-description">较低的值 (0-0.5) 使输出更确定，较高的值 (0.7-2)
                                                使输出更创意随机</span>
                                        </div>
                                        <div class="settings-control">
                                            <input type="range" id="ai-temperature" min="0" max="2" step="0.1"
                                                value="0.7">
                                            <span id="temperature-value">0.7</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮卡片 -->
                                <div class="settings-card">
                                    <div class="settings-actions">
                                        <div class="primary-actions">
                                            <button class="settings-button primary-button save-button">保存设置</button>
                                        </div>
                                        <div class="secondary-actions">
                                            <button id="test-ai-connection" class="settings-button secondary-button">测试
                                                API 连接</button>
                                            <button id="clear-ai-settings" class="settings-button danger-button">清除 AI
                                                设置及历史</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 外观设置 - 替换为Coming Soon提示 -->
                        <div class="settings-panel" id="appearance-panel">
                            <h3>外观设置</h3>
                            <div class="settings-panel-content">
                                <div class="settings-card">
                                    <div class="coming-soon-container">
                                        <div class="coming-soon-icon">
                                            <i class="icon-paintbrush"></i>
                                        </div>
                                        <h4 class="coming-soon-title">即将上线</h4>
                                        <p class="coming-soon-description">
                                            我们正在开发更丰富的自定义外观功能，包括主题颜色、字体样式和布局选项。
                                            <br>敬请期待！
                                        </p>
                                        <div class="coming-soon-decoration">
                                            <span class="decoration-dot"></span>
                                            <span class="decoration-dot"></span>
                                            <span class="decoration-dot"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 备份恢复 -->
                        <div class="settings-panel" id="backup-panel">
                            <h3>备份与恢复</h3>
                            <div class="settings-panel-content">
                                <!-- 导出卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>数据导出</h4>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>导出便签 (JSON)</label>
                                            <span class="settings-description">将所有便签导出为JSON文件，可通过下方导入功能恢复</span>
                                        </div>
                                        <div class="settings-control">
                                            <button class="settings-button secondary-button" id="export-notes-json">导出便签
                                                (JSON)</button>
                                        </div>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>导出完整备份 (.db)</label>
                                            <span class="settings-description">下载包含所有便签和设置的数据库文件，用于手动恢复或迁移</span>
                                        </div>
                                        <div class="settings-control">
                                            <button class="settings-button secondary-button"
                                                id="export-database-db">导出数据库 (.db)</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 导入卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>数据导入</h4>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>导入便签 (JSON)</label>
                                            <span class="settings-description">从JSON文件导入便签数据 (将替换现有便签)</span>
                                        </div>
                                        <div class="settings-control">
                                            <label for="import-file" class="file-input-label">选择 JSON 文件</label>
                                            <input type="file" id="import-file" accept=".json" class="file-input">
                                        </div>
                                    </div>
                                    <!-- 备份操作状态提示区域 -->
                                    <div id="backup-status-area" class="settings-status"></div>
                                </div>

                                <!-- 重置卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>重置选项</h4>
                                    </div>
                                    <div class="settings-item">
                                        <div class="settings-label">
                                            <label>重置所有设置</label>
                                            <span class="settings-description">将所有设置恢复为默认值，不会删除您的便签数据</span>
                                        </div>
                                        <div class="settings-control">
                                            <button class="settings-button danger-button reset-button">重置所有设置</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 关于 -->
                        <div class="settings-panel" id="about-panel">
                            <h3>关于</h3>
                            <div class="settings-panel-content">
                                <div class="settings-card">
                                    <div class="about-content">
                                        <img src="img/logo.png" alt="Logo" class="app-logo">
                                        <h2>InfinityNotes - 无限便签</h2>
                                        <p class="version">版本 1.0.0</p>
                                        <p class="description">
                                            一个结合了无限画布和 AI 能力的便签应用，帮助您捕捉灵感、整理思路，并利用 AI 扩展您的想法。
                                        </p>
                                        <div class="links">
                                            <a href="https://github.com/yourusername/infinity-notes"
                                                target="_blank">GitHub</a>
                                            <a href="#" target="_blank">反馈问题</a>
                                            <a href="#" target="_blank">帮助文档</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 个人中心面板 -->
                        <div class="settings-panel" id="profile-panel">
                            <h3>个人中心</h3>
                            <div class="settings-panel-content">
                                <!-- 个人信息卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>用户信息</h4>
                                    </div>
                                    <div class="profile-header">
                                        <div class="profile-avatar">
                                            <i class="icon-user">👤</i>
                                        </div>
                                        <div class="profile-info">
                                            <h2 class="profile-name">admin</h2>
                                            <span class="profile-badge">管理员</span>
                                        </div>
                                    </div>
                                    <div class="profile-actions">
                                        <button id="settings-logout-button"
                                            class="settings-button secondary-button logout-button">
                                            <i class="icon-logout">⎋</i>
                                            <span>退出登录</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- 安全设置卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>安全设置</h4>
                                    </div>
                                    <div class="password-form">
                                        <div class="settings-item">
                                            <div class="settings-label">
                                                <label for="current-password">当前密码</label>
                                            </div>
                                            <div class="settings-control">
                                                <input type="password" id="current-password" class="settings-input"
                                                    placeholder="输入当前密码">
                                            </div>
                                        </div>
                                        <div class="settings-item">
                                            <div class="settings-label">
                                                <label for="new-password">新密码</label>
                                            </div>
                                            <div class="settings-control">
                                                <input type="password" id="new-password" class="settings-input"
                                                    placeholder="输入新密码">
                                            </div>
                                        </div>
                                        <div class="settings-item">
                                            <div class="settings-label">
                                                <label for="confirm-password">确认新密码</label>
                                            </div>
                                            <div class="settings-control">
                                                <input type="password" id="confirm-password" class="settings-input"
                                                    placeholder="再次输入新密码">
                                            </div>
                                        </div>
                                        <div class="settings-actions">
                                            <div class="primary-actions">
                                                <button id="change-password-button"
                                                    class="settings-button primary-button">更新密码</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 移除邀请码管理卡片，已移至用户管理面板 -->
                            </div>
                        </div>

                        <!-- 用户管理面板 (仅管理员可见) -->
                        <div class="settings-panel" id="users-panel">
                            <h3>用户管理</h3>
                            <div class="settings-panel-content">
                                <!-- 邀请码管理卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>邀请码管理</h4>
                                    </div>
                                    <div id="invite-code-manager-container">
                                        <!-- 邀请码管理器将通过JavaScript动态添加 -->
                                    </div>
                                </div>

                                <!-- 用户统计卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>用户统计</h4>
                                    </div>
                                    <div class="user-stats">
                                        <div class="stat-item">
                                            <div class="stat-label">总用户数</div>
                                            <div class="stat-value" id="total-users-count">-</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-label">今日新增</div>
                                            <div class="stat-value" id="new-users-today">-</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 用户列表卡片 -->
                                <div class="settings-card">
                                    <div class="settings-card-header">
                                        <h4>用户列表</h4>
                                    </div>

                                    <!-- 用户列表工具栏 -->
                                    <div class="users-toolbar">
                                        <div class="users-search-container">
                                            <input type="text" id="users-search" class="users-search"
                                                placeholder="搜索用户...">
                                            <i class="search-icon">🔍</i>
                                        </div>
                                        <div class="users-actions">
                                            <button id="refresh-users-list"
                                                class="settings-button secondary-button user-toolbar-button">
                                                <i class="icon-refresh">🔄</i> 刷新
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 用户表格容器 -->
                                    <div class="users-table-container">
                                        <table class="users-table">
                                            <thead>
                                                <tr>
                                                    <th class="sortable" data-sort="username">
                                                        <div class="th-content">
                                                            <span>用户名</span>
                                                            <i class="sort-icon"></i>
                                                        </div>
                                                    </th>
                                                    <th class="sortable" data-sort="createdAt">
                                                        <div class="th-content">
                                                            <span>创建时间</span>
                                                            <i class="sort-icon"></i>
                                                        </div>
                                                    </th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="users-list">
                                                <!-- 用户列表将通过JavaScript动态添加 -->
                                            </tbody>
                                        </table>

                                        <!-- 空状态显示 -->
                                        <div class="empty-state" id="no-users">
                                            <p>正在加载用户列表...</p>
                                        </div>
                                    </div>

                                    <!-- 分页控制 -->
                                    <div class="users-pagination">
                                        <div class="pagination-info">显示 <span id="pagination-start">1</span>-<span
                                                id="pagination-end">10</span> 共 <span id="pagination-total">0</span> 条
                                        </div>
                                        <div class="pagination-controls">
                                            <button id="pagination-prev" class="pagination-button" disabled>上一页</button>
                                            <button id="pagination-next" class="pagination-button" disabled>下一页</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 便签合集弹窗 -->
        <div class="reading-modal" id="reading-modal">
            <div class="reading-container">
                <div class="reading-header">
                    <h2>便签合集</h2>
                    <button class="close-reading zoom-btn" id="close-reading">&times;</button>
                </div>

                <div class="reading-content">
                    <!-- 左侧便签列表 -->
                    <div class="reading-nav">
                        <!-- 便签列表会通过 JavaScript 动态添加 -->
                        <!-- 结构示例: -->
                        <!-- <button class="nav-item active" data-note-id="123">便签标题 1</button> -->
                        <!-- <button class="nav-item" data-note-id="456">便签标题 2</button> -->
                    </div>

                    <!-- 右侧便签内容 -->
                    <div class="reading-panels">
                        <!-- 内容面板会通过 JavaScript 动态添加 -->
                        <!-- 结构示例: -->
                        <!-- <div class="reading-panel active" id="note-123-panel">
                            <div class="note-content markdown-content">便签内容...</div>
                        </div> -->

                        <!-- 无便签时显示的信息 -->
                        <div class="no-notes-message" id="no-notes-message">
                            <p>暂无便签可阅读</p>
                            <p>请先添加便签，或通过 AI 生成便签</p>
                        </div>

                        <!-- 功能区域 - 仅在右侧文本区域下方 -->
                        <div class="reading-panel-footer">
                            <div class="reading-panel-actions">
                                <!-- 这里将来可以放置各种功能按钮 -->
                                <div class="reading-actions-placeholder">功能区域（待开发）</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 引入JavaScript文件 -->
        <!-- 使用 ES Modules 格式引入主应用文件 -->
        <script type="module" src="js/app.js"></script>
    </div>
</body>

</html>