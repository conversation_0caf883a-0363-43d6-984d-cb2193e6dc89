# InfinityNotes - 无限便签

这是一个基于 Node.js 的 Web 应用项目，允许用户创建、编辑和管理便签，并且支持使用 AI 生成便签内容。所有数据（便签和 AI 设置）都存储在本地 SQLite 数据库中。

## 功能特性

- 创建和编辑带有 Markdown 支持的便签
- 支持自由拖动和调整便签大小
- 通过 AI 生成便签内容（需要配置 API）
- 可调整画布缩放和平移
- 内置网页快捷访问功能
- **数据存储:** 所有便签和 AI 配置存储在本地 SQLite 数据库 (`data/notes.db`)
- **数据管理:**
  - 通过 UI 配置 AI API (密钥、URL、模型等)
  - 导出便签数据为 JSON 文件
  - 导入 JSON 文件以恢复便签数据 (会覆盖现有便签)
  - 导出完整的数据库文件 (`.db`) 作为备份
  - 重置所有便签和设置

## 项目结构

- `/public` - 静态资源文件 (HTML, CSS, 前端 JS)
- `/server` - 服务器端 Node.js 代码 (Express, API 路由, 数据库交互, AI 服务)
- `/data` - 数据存储目录，包含 `notes.db` SQLite 数据库文件
- `/logs` - 日志文件目录 (自动创建)

## 安装

1. 克隆代码库：
   ```bash
   git clone <repository-url>
   cd infinity-notes
   ```
2. 安装依赖：
   ```bash
   npm install
   ```
   _注意: `sqlite3` 是原生模块，可能需要在目标系统上编译或下载预编译包。确保安装了 Node.js (>=16) 和 npm。_
3. 数据库和配置：
   - 数据库文件 (`data/notes.db`) 会在首次启动时自动创建。
   - AI API 配置通过应用程序的设置界面进行管理，并存储在数据库中。不再需要 `.env` 文件进行核心配置。

## 运行

开发模式 (使用 nodemon 自动重启):

```bash
npm run dev
```

生产模式:

```bash
npm start
```

应用启动后，访问 `http://localhost:3000` (或指定的端口)。

## 安全说明

- **AI API 密钥:** 通过应用的设置界面输入，存储在本地数据库中。请确保运行服务器的环境安全。
- **数据备份:**
  - **便签备份 (JSON):** 可通过设置界面导出，用于在应用内恢复便签列表。
  - **完整备份 (.db):** 可通过设置界面导出 `notes.db` 文件。此文件包含所有便签和设置，主要用于手动恢复或迁移，**无法通过应用界面直接导入**。请定期备份此文件。
- **访问控制:** 应用目前没有用户认证系统，任何能访问服务器端口的人都可以使用。请勿在不受信任的网络中暴露服务。
- **依赖安全:** 定期运行 `npm audit` 检查并修复依赖项中的安全漏洞。

## 技术栈

- **前端:** 原生 JavaScript, HTML5, CSS3, Marked.js (Markdown 解析)
- **后端:** Node.js, Express.js
- **数据库:** SQLite (使用 `sqlite3` 包)
- **AI API:** OpenAI compatible (通过用户配置连接)

## 贡献

欢迎提交 PR 或 Issue。在开始任何重大工作之前，请先在 Issue 中讨论你想要做的改变。

<!-- ## 优化建议和冗余代码分析

1. 服务器端优化建议
   1.1 路由重复定义
   在 server/routes.js 中，有重复定义的路由：

第 54 行和第 1104 行都定义了/api/health 路由，这会导致第二个定义覆盖第一个。
第 891 行和第 810 行都定义了/stream-connection/:sessionId 路由，这会导致冲突。
1.2 未使用的导入和变量
在 server/routes.js 中，有一些导入的函数没有被使用，例如 updateUserShareInfo 和 closeUserShare。
在 server/ai_service.js 中，lastChunkTime 变量被定义但没有被充分利用。
1.3 冗余的验证逻辑
在 server/routes.js 中，有多处重复的认证检查逻辑，例如：

第 1116-1122 行和第 1141-1147 行对管理员权限的检查逻辑几乎完全相同。
第 1166-1172 行也是类似的重复代码。
这些可以抽取为一个中间件函数，例如 requireAdmin。

1.4 数据库连接管理
在 server/database.js 中，数据库连接是全局变量，没有提供关闭连接的方法，这可能导致在应用关闭时资源未被正确释放。

2. 前端优化建议
   2.1 App.js 中的重复代码
   在 public/js/modules/app/App.js 中：
   第 42-68 行有两个非常相似的对象定义：this.activeSession 和 this.sessionManager，包含许多重复的属性。
   第 444-530 行和第 500-529 行的代码块非常相似，处理多次点击确认的逻辑可以抽取为一个通用函数。
   2.2 Canvas.js 中的冗余代码
   在 public/js/modules/canvas/Canvas.js 中：
   第 108-234 行的装饰性背景元素创建函数可以简化，三个函数（addDots、addGradientBubbles、addDecorativeLines）有很多重复的逻辑。
   第 616-730 行和第 738-920 行的对话框创建和事件处理逻辑有大量重复，可以抽取为一个通用的对话框创建函数。
   2.3 Note.js 中的优化机会
   在 public/js/modules/note/Note.js 中：

第 273-380 行的事件处理函数过长，可以拆分为更小的函数以提高可维护性。
第 448-483 行的 update 方法中有一些冗余的条件判断。

3.  性能优化建议
    3.1 服务器端性能优化
    在 server/routes.js 中，第 856-880 行的连接清理定时器可以优化为只在有连接时才执行检查，避免不必要的空循环。
    在 server/database.js 中，第 455-568 行的 importNotes 函数使用了串行处理，可以考虑批量插入以提高性能。
    3.2 前端性能优化
    在 public/js/modules/app/App.js 中，第 179-264 行的预连接逻辑可能导致不必要的网络请求，可以添加更智能的触发条件。
    在 public/js/modules/canvas/Canvas.js 中，第 398-404 行的画布移动没有使用 requestAnimationFrame，可能导致在低性能设备上的卡顿。 -->

     4. 代码结构优化建议
    4.1 模块化改进
    将 server/routes.js 拆分为多个路由文件，按功能分组（认证路由、便签路由、AI 路由等）。
    将 public/js/modules/app/App.js 拆分为更小的模块，例如将 AI 相关功能、设置管理、便签管理分离为独立模块。
    4.2 配置管理优化
    创建一个集中的配置管理模块，替代散布在各处的配置常量。
    使用环境变量替代硬编码的配置值，特别是在 server/ai_service.js 中的应用信息。
    4.3 错误处理统一
    创建统一的错误处理中间件，替代各个路由中重复的错误处理逻辑。
    前端实现统一的错误处理机制，避免在每个函数中重复类似的错误处理代码。
    5. 安全性优化建议
    5.1 API 密钥保护
    在 server/ai_service.js 中，API 密钥直接存储在内存中，可以考虑使用加密存储或更安全的凭证管理方式。
    在前端显示 API 密钥时（如 App.js 中的切换显示功能），应该有更严格的访问控制。
    5.2 输入验证增强
    在 server/routes.js 中，对用户输入的验证可以更严格，特别是对 API 路径参数的验证。
    在前端表单提交前，应该增加更全面的输入验证。
    6. 测试相关建议
    6.1 缺少测试代码
    项目中没有看到测试文件，建议添加单元测试和集成测试。
    为关键功能（如 AI 生成、便签操作、用户认证）编写测试用例。
    6.2 测试辅助功能
    添加测试模式配置，允许在测试环境中模拟 AI 服务响应。
    实现测试数据生成器，用于创建测试数据。
    总结
    项目整体结构良好，但存在一些冗余代码和优化机会。主要问题集中在：

路由定义重复和冗余验证逻辑
前端组件中的重复代码块
性能优化空间，特别是在数据库操作和前端动画方面
缺少测试代码和统一的错误处理
